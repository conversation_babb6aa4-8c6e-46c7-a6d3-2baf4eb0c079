#!/bin/bash

# 服务器监控管理脚本

echo "🖥️  服务器监控管理中心"
echo "======================"

# 检查Prometheus是否运行
if ! curl -s http://localhost:9090/api/health > /dev/null; then
    echo "❌ Prometheus未运行，请先启动监控服务"
    echo "运行: docker compose up -d"
    exit 1
fi

# 获取所有监控目标
get_all_targets() {
    curl -s "http://localhost:9090/api/v1/targets" | jq -r '.data.activeTargets[] | select(.labels.job | contains("server") or contains("node") or contains("local")) | "\(.labels.instance)|\(.health)|\(.labels.job)|\(.labels.server_name // "未命名")|\(.labels.server_type // "未分类")"' 2>/dev/null
}

# 显示服务器列表
show_servers() {
    echo ""
    echo "📊 当前监控的服务器："
    echo "===================="
    
    printf "%-20s %-8s %-15s %-12s %-15s\n" "服务器地址" "状态" "任务名称" "服务器名" "类型"
    printf "%-20s %-8s %-15s %-12s %-15s\n" "--------------------" "--------" "---------------" "------------" "---------------"
    
    get_all_targets | while IFS='|' read -r instance health job server_name server_type; do
        if [ "$health" = "up" ]; then
            status="✅ 正常"
        else
            status="❌ 异常"
        fi
        printf "%-20s %-8s %-15s %-12s %-15s\n" "$instance" "$status" "$job" "$server_name" "$server_type"
    done
    
    echo ""
    
    # 统计信息
    total_servers=$(get_all_targets | wc -l)
    up_servers=$(get_all_targets | grep -c "|up|")
    down_servers=$(get_all_targets | grep -c "|down|")
    
    echo "📈 统计信息："
    echo "  - 总服务器数: $total_servers"
    echo "  - 正常运行: $up_servers"
    echo "  - 异常服务器: $down_servers"
}

# 显示服务器详细信息
show_server_details() {
    read -p "🔍 请输入服务器IP地址: " server_ip
    
    echo ""
    echo "📋 服务器详细信息: $server_ip"
    echo "=========================="
    
    # 检查服务器状态
    target_info=$(curl -s "http://localhost:9090/api/v1/targets" | jq -r ".data.activeTargets[] | select(.labels.instance==\"$server_ip:9100\")" 2>/dev/null)
    
    if [ -z "$target_info" ]; then
        echo "❌ 未找到服务器 $server_ip"
        return
    fi
    
    health=$(echo "$target_info" | jq -r '.health')
    job=$(echo "$target_info" | jq -r '.labels.job')
    server_name=$(echo "$target_info" | jq -r '.labels.server_name // "未命名"')
    server_type=$(echo "$target_info" | jq -r '.labels.server_type // "未分类"')
    last_scrape=$(echo "$target_info" | jq -r '.lastScrape')
    
    echo "  - 服务器名称: $server_name"
    echo "  - 服务器类型: $server_type"
    echo "  - 监控任务: $job"
    echo "  - 健康状态: $health"
    echo "  - 最后采集: $last_scrape"
    
    if [ "$health" = "up" ]; then
        echo ""
        echo "📊 实时指标："
        
        # CPU使用率
        cpu_usage=$(curl -s "http://localhost:9090/api/v1/query?query=100%20-%20(avg%20by%20(instance)%20(irate(node_cpu_seconds_total%7Binstance%3D%22$server_ip:9100%22%2Cmode%3D%22idle%22%7D%5B5m%5D))%20*%20100)" | jq -r '.data.result[0].value[1]' 2>/dev/null)
        
        # 内存使用率
        mem_usage=$(curl -s "http://localhost:9090/api/v1/query?query=(1%20-%20(node_memory_MemAvailable_bytes%7Binstance%3D%22$server_ip:9100%22%7D%20%2F%20node_memory_MemTotal_bytes%7Binstance%3D%22$server_ip:9100%22%7D))%20*%20100" | jq -r '.data.result[0].value[1]' 2>/dev/null)
        
        # 磁盘使用率
        disk_usage=$(curl -s "http://localhost:9090/api/v1/query?query=(1%20-%20(node_filesystem_avail_bytes%7Binstance%3D%22$server_ip:9100%22%2Cfstype!%3D%22tmpfs%22%7D%20%2F%20node_filesystem_size_bytes%7Binstance%3D%22$server_ip:9100%22%2Cfstype!%3D%22tmpfs%22%7D))%20*%20100" | jq -r '.data.result[0].value[1]' 2>/dev/null)
        
        if [ "$cpu_usage" != "null" ] && [ -n "$cpu_usage" ]; then
            printf "  - CPU 使用率: %.2f%%\n" "$cpu_usage"
        fi
        
        if [ "$mem_usage" != "null" ] && [ -n "$mem_usage" ]; then
            printf "  - 内存使用率: %.2f%%\n" "$mem_usage"
        fi
        
        if [ "$disk_usage" != "null" ] && [ -n "$disk_usage" ]; then
            printf "  - 磁盘使用率: %.2f%%\n" "$disk_usage"
        fi
    fi
}

# 测试服务器连通性
test_connectivity() {
    read -p "🔗 请输入服务器IP地址: " server_ip
    
    echo ""
    echo "🔍 测试服务器连通性: $server_ip"
    echo "=========================="
    
    # 测试端口连通性
    if timeout 5 bash -c "</dev/tcp/$server_ip/9100" 2>/dev/null; then
        echo "✅ 端口 9100 连通正常"
    else
        echo "❌ 端口 9100 连接失败"
        echo "请检查："
        echo "  - 服务器是否在线"
        echo "  - Node Exporter是否运行"
        echo "  - 防火墙是否开放9100端口"
        return
    fi
    
    # 测试HTTP响应
    if curl -s --connect-timeout 5 http://$server_ip:9100/metrics > /dev/null; then
        echo "✅ HTTP响应正常"
        
        # 获取一些基本指标
        metrics_count=$(curl -s http://$server_ip:9100/metrics | wc -l)
        echo "✅ 指标数量: $metrics_count 条"
    else
        echo "❌ HTTP响应失败"
    fi
}

# 移除服务器
remove_server() {
    echo ""
    echo "🗑️  移除服务器监控"
    echo "=================="
    
    show_servers
    
    read -p "请输入要移除的服务器IP地址: " server_ip
    
    if ! grep -q "$server_ip:9100" prometheus/prometheus.yml; then
        echo "❌ 服务器 $server_ip 不在配置中"
        return
    fi
    
    echo "⚠️  即将移除服务器: $server_ip"
    read -p "确认移除？(y/n): " confirm
    
    if [[ $confirm != "y" && $confirm != "Y" ]]; then
        echo "❌ 操作已取消"
        return
    fi
    
    # 备份配置
    cp prometheus/prometheus.yml prometheus/prometheus.yml.backup.$(date +%Y%m%d_%H%M%S)
    
    # 移除服务器行
    sed -i "/$server_ip:9100/d" prometheus/prometheus.yml
    
    # 重启Prometheus
    docker compose restart prometheus
    
    echo "✅ 服务器 $server_ip 已从监控中移除"
}

# 主菜单
while true; do
    echo ""
    echo "🎯 请选择操作："
    echo "1. 查看所有服务器"
    echo "2. 查看服务器详情"
    echo "3. 测试服务器连通性"
    echo "4. 添加新服务器"
    echo "5. 移除服务器"
    echo "6. 打开Grafana面板"
    echo "7. 打开Prometheus Targets"
    echo "0. 退出"
    echo ""
    
    read -p "请输入选项 (0-7): " choice
    
    case $choice in
        1)
            show_servers
            ;;
        2)
            show_server_details
            ;;
        3)
            test_connectivity
            ;;
        4)
            echo ""
            echo "🚀 添加新服务器："
            echo "请运行: ./add-remote-server.sh (自动化)"
            echo "或运行: ./quick-add-server.sh (手动配置)"
            ;;
        5)
            remove_server
            ;;
        6)
            echo "🌐 正在打开Grafana面板..."
            if command -v xdg-open &> /dev/null; then
                xdg-open http://localhost:3000
            elif command -v open &> /dev/null; then
                open http://localhost:3000
            else
                echo "请手动访问: http://localhost:3000"
            fi
            ;;
        7)
            echo "🌐 正在打开Prometheus Targets..."
            if command -v xdg-open &> /dev/null; then
                xdg-open http://localhost:9090/targets
            elif command -v open &> /dev/null; then
                open http://localhost:9090/targets
            else
                echo "请手动访问: http://localhost:9090/targets"
            fi
            ;;
        0)
            echo "👋 再见！"
            exit 0
            ;;
        *)
            echo "❌ 无效选项，请重新选择"
            ;;
    esac
done
