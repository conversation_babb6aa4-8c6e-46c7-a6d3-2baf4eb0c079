# 🌐 添加其他服务器到监控系统

## 📋 概述

要将其他服务器添加到监控系统中，需要完成以下步骤：
1. 在远程服务器上部署 Node Exporter
2. 配置防火墙和网络
3. 更新 Prometheus 配置
4. 验证监控状态

## 🚀 方法一：使用 Docker 部署（推荐）

### 步骤 1: 在远程服务器上部署 Node Exporter

#### 1.1 复制部署文件到远程服务器
```bash
# 将 remote-node-exporter.yml 复制到远程服务器
scp remote-node-exporter.yml user@远程服务器IP:/home/<USER>/

# 或者直接在远程服务器上创建文件
```

#### 1.2 在远程服务器上启动 Node Exporter
```bash
# SSH 登录到远程服务器
ssh user@远程服务器IP

# 启动 Node Exporter
docker-compose -f remote-node-exporter.yml up -d

# 检查运行状态
docker ps | grep node-exporter
```

#### 1.3 验证 Node Exporter 运行
```bash
# 在远程服务器上测试
curl http://localhost:9100/metrics

# 从监控服务器测试连通性
curl http://远程服务器IP:9100/metrics
```

### 步骤 2: 配置防火墙

#### 2.1 开放 9100 端口
```bash
# Ubuntu/Debian
sudo ufw allow 9100

# CentOS/RHEL
sudo firewall-cmd --permanent --add-port=9100/tcp
sudo firewall-cmd --reload

# 或者针对特定IP开放
sudo ufw allow from 监控服务器IP to any port 9100
```

### 步骤 3: 更新 Prometheus 配置

#### 3.1 编辑 prometheus.yml
在监控服务器上编辑 `prometheus/prometheus.yml`：

```yaml
  # 远程服务器监控
  - job_name: 'remote-servers'
    static_configs:
      - targets: 
        - '*************:9100'  # 服务器1
        - '*************:9100'  # 服务器2
        - '新服务器IP:9100'      # 添加新服务器
        labels:
          server_type: 'remote-server'
          environment: 'production'
    scrape_interval: 5s
    metrics_path: /metrics
```

#### 3.2 重启 Prometheus
```bash
docker compose restart prometheus
```

## 🛠️ 方法二：直接安装 Node Exporter

### 在远程服务器上直接安装

#### Ubuntu/Debian:
```bash
# 下载 Node Exporter
wget https://github.com/prometheus/node_exporter/releases/download/v1.6.1/node_exporter-1.6.1.linux-amd64.tar.gz

# 解压
tar xvfz node_exporter-1.6.1.linux-amd64.tar.gz

# 移动到系统目录
sudo mv node_exporter-1.6.1.linux-amd64/node_exporter /usr/local/bin/

# 创建用户
sudo useradd --no-create-home --shell /bin/false node_exporter

# 创建 systemd 服务文件
sudo tee /etc/systemd/system/node_exporter.service > /dev/null <<EOF
[Unit]
Description=Node Exporter
Wants=network-online.target
After=network-online.target

[Service]
User=node_exporter
Group=node_exporter
Type=simple
ExecStart=/usr/local/bin/node_exporter

[Install]
WantedBy=multi-user.target
EOF

# 启动服务
sudo systemctl daemon-reload
sudo systemctl start node_exporter
sudo systemctl enable node_exporter
```

#### CentOS/RHEL:
```bash
# 类似步骤，或使用 yum/dnf
sudo yum install -y node_exporter
sudo systemctl start node_exporter
sudo systemctl enable node_exporter
```

## 📊 高级配置

### 为不同服务器添加标签

```yaml
  - job_name: 'web-servers'
    static_configs:
      - targets: ['************:9100', '************:9100']
        labels:
          server_type: 'web-server'
          environment: 'production'
          datacenter: 'dc1'

  - job_name: 'database-servers'
    static_configs:
      - targets: ['************:9100', '************:9100']
        labels:
          server_type: 'database-server'
          environment: 'production'
          datacenter: 'dc1'

  - job_name: 'test-servers'
    static_configs:
      - targets: ['*************:9100']
        labels:
          server_type: 'test-server'
          environment: 'testing'
          datacenter: 'dc2'
```

### 配置不同的采集间隔

```yaml
  - job_name: 'critical-servers'
    static_configs:
      - targets: ['************:9100']
    scrape_interval: 5s    # 高频采集

  - job_name: 'normal-servers'
    static_configs:
      - targets: ['************:9100']
    scrape_interval: 15s   # 正常频率

  - job_name: 'backup-servers'
    static_configs:
      - targets: ['************:9100']
    scrape_interval: 60s   # 低频采集
```

## 🔍 验证和故障排除

### 检查监控状态
```bash
# 检查 Prometheus targets
curl -s "http://localhost:9090/api/v1/targets" | jq '.data.activeTargets[] | {job: .labels.job, instance: .labels.instance, health: .health}'

# 检查特定服务器的指标
curl -s "http://localhost:9090/api/v1/query?query=up{instance=\"远程服务器IP:9100\"}"
```

### 常见问题解决

#### 1. 连接超时
- 检查防火墙设置
- 验证网络连通性: `telnet 远程服务器IP 9100`
- 确认 Node Exporter 正在运行

#### 2. 权限问题
- 确保 Node Exporter 有读取系统信息的权限
- 检查 Docker 容器的特权设置

#### 3. 数据不准确
- 验证时间同步: `ntpdate -s time.nist.gov`
- 检查系统时区设置

## 🔒 安全建议

### 网络安全
```bash
# 只允许监控服务器访问
sudo ufw allow from 监控服务器IP to any port 9100
sudo ufw deny 9100

# 使用 VPN 或内网连接
# 配置 SSL/TLS 加密（高级）
```

### 认证配置（可选）
```yaml
  - job_name: 'secure-servers'
    static_configs:
      - targets: ['************:9100']
    basic_auth:
      username: 'monitoring'
      password: 'secure_password'
```

## 📈 监控效果

添加成功后，您将在 Grafana 中看到：
- 🖥️ 新服务器的 CPU、内存、磁盘使用率
- 🌐 网络流量统计
- 📊 系统负载和进程信息
- 🔔 基于预设规则的告警

## 💡 最佳实践

1. **标准化命名**: 使用一致的服务器命名规则
2. **分组管理**: 按功能或环境对服务器分组
3. **监控分级**: 对不同重要性的服务器设置不同的监控频率
4. **文档记录**: 维护服务器清单和配置文档
5. **定期检查**: 定期验证所有服务器的监控状态

现在您可以开始添加其他服务器到监控系统中了！
