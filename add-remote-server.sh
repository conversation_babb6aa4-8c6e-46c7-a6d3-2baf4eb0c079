#!/bin/bash

# 添加远程服务器到监控系统的自动化脚本

echo "🌐 添加远程服务器到监控系统"
echo "=============================="

# 检查必要的工具
if ! command -v jq &> /dev/null; then
    echo "❌ 需要安装 jq 工具"
    echo "Ubuntu/Debian: sudo apt install jq"
    echo "CentOS/RHEL: sudo yum install jq"
    exit 1
fi

# 获取用户输入
echo ""
echo "📝 请输入远程服务器信息："
echo ""

read -p "🖥️  服务器IP地址: " SERVER_IP
read -p "🏷️  服务器名称 (可选): " SERVER_NAME
read -p "🔧 服务器类型 (web/database/app/other): " SERVER_TYPE
read -p "🌍 环境 (production/testing/development): " ENVIRONMENT
read -p "👤 SSH用户名: " SSH_USER

# 验证IP地址格式
if ! [[ $SERVER_IP =~ ^[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}$ ]]; then
    echo "❌ IP地址格式不正确"
    exit 1
fi

# 设置默认值
SERVER_NAME=${SERVER_NAME:-"server-$(echo $SERVER_IP | tr '.' '-')"}
SERVER_TYPE=${SERVER_TYPE:-"other"}
ENVIRONMENT=${ENVIRONMENT:-"production"}

echo ""
echo "📋 配置信息确认："
echo "  - IP地址: $SERVER_IP"
echo "  - 服务器名称: $SERVER_NAME"
echo "  - 服务器类型: $SERVER_TYPE"
echo "  - 环境: $ENVIRONMENT"
echo "  - SSH用户: $SSH_USER"
echo ""

read -p "确认添加此服务器？(y/n): " confirm
if [[ $confirm != "y" && $confirm != "Y" ]]; then
    echo "❌ 操作已取消"
    exit 0
fi

echo ""
echo "🚀 开始添加服务器..."

# 步骤1: 测试SSH连接
echo ""
echo "1️⃣ 测试SSH连接..."
if ssh -o ConnectTimeout=10 -o BatchMode=yes $SSH_USER@$SERVER_IP exit 2>/dev/null; then
    echo "✅ SSH连接成功"
else
    echo "❌ SSH连接失败，请检查："
    echo "   - 服务器IP地址是否正确"
    echo "   - SSH用户名是否正确"
    echo "   - SSH密钥是否已配置"
    echo "   - 服务器是否在线"
    exit 1
fi

# 步骤2: 复制Node Exporter配置文件
echo ""
echo "2️⃣ 复制Node Exporter配置文件到远程服务器..."
if scp remote-node-exporter.yml $SSH_USER@$SERVER_IP:/tmp/; then
    echo "✅ 配置文件复制成功"
else
    echo "❌ 配置文件复制失败"
    exit 1
fi

# 步骤3: 在远程服务器上部署Node Exporter
echo ""
echo "3️⃣ 在远程服务器上部署Node Exporter..."

DEPLOY_SCRIPT="
# 检查Docker是否安装
if ! command -v docker &> /dev/null; then
    echo '❌ Docker未安装，请先安装Docker'
    exit 1
fi

# 检查Docker Compose
if docker compose version &> /dev/null; then
    DOCKER_COMPOSE='docker compose'
elif command -v docker-compose &> /dev/null; then
    DOCKER_COMPOSE='docker-compose'
else
    echo '❌ Docker Compose未安装'
    exit 1
fi

# 停止可能存在的旧容器
\$DOCKER_COMPOSE -f /tmp/remote-node-exporter.yml down 2>/dev/null || true

# 启动Node Exporter
\$DOCKER_COMPOSE -f /tmp/remote-node-exporter.yml up -d

# 等待启动
sleep 10

# 检查状态
if curl -s http://localhost:9100/metrics > /dev/null; then
    echo '✅ Node Exporter启动成功'
    
    # 开放防火墙端口（如果有ufw）
    if command -v ufw &> /dev/null; then
        sudo ufw allow 9100 2>/dev/null || true
        echo '✅ 防火墙端口已开放'
    fi
    
    exit 0
else
    echo '❌ Node Exporter启动失败'
    exit 1
fi
"

if ssh $SSH_USER@$SERVER_IP "$DEPLOY_SCRIPT"; then
    echo "✅ 远程服务器Node Exporter部署成功"
else
    echo "❌ 远程服务器Node Exporter部署失败"
    exit 1
fi

# 步骤4: 测试连通性
echo ""
echo "4️⃣ 测试监控连通性..."
if curl -s --connect-timeout 10 http://$SERVER_IP:9100/metrics > /dev/null; then
    echo "✅ 监控连通性测试成功"
else
    echo "❌ 监控连通性测试失败，请检查："
    echo "   - 防火墙是否开放9100端口"
    echo "   - Node Exporter是否正常运行"
    echo "   - 网络连接是否正常"
    exit 1
fi

# 步骤5: 更新Prometheus配置
echo ""
echo "5️⃣ 更新Prometheus配置..."

# 备份原配置
cp prometheus/prometheus.yml prometheus/prometheus.yml.backup.$(date +%Y%m%d_%H%M%S)

# 检查是否已存在该服务器
if grep -q "$SERVER_IP:9100" prometheus/prometheus.yml; then
    echo "⚠️  服务器 $SERVER_IP 已存在于配置中"
else
    # 创建新的服务器条目
    NEW_SERVER_ENTRY="        - '$SERVER_IP:9100'  # $SERVER_NAME"
    
    # 在remote-servers部分添加新服务器
    if grep -q "# 服务器1" prometheus/prometheus.yml; then
        # 在第一个示例服务器前插入
        sed -i "/# 服务器1/i\\$NEW_SERVER_ENTRY" prometheus/prometheus.yml
    else
        # 在targets数组中添加
        sed -i "/- targets:/a\\$NEW_SERVER_ENTRY" prometheus/prometheus.yml
    fi
    
    echo "✅ Prometheus配置已更新"
fi

# 步骤6: 重启Prometheus
echo ""
echo "6️⃣ 重启Prometheus服务..."
if docker compose restart prometheus; then
    echo "✅ Prometheus重启成功"
else
    echo "❌ Prometheus重启失败"
    exit 1
fi

# 步骤7: 验证监控状态
echo ""
echo "7️⃣ 验证监控状态..."
sleep 15  # 等待Prometheus重新加载配置

# 检查target状态
TARGET_STATUS=$(curl -s "http://localhost:9090/api/v1/targets" | jq -r ".data.activeTargets[] | select(.labels.instance==\"$SERVER_IP:9100\") | .health")

if [ "$TARGET_STATUS" = "up" ]; then
    echo "✅ 服务器监控状态正常"
else
    echo "⚠️  服务器监控状态: $TARGET_STATUS"
    echo "请检查Prometheus Targets页面: http://localhost:9090/targets"
fi

# 完成总结
echo ""
echo "🎉 服务器添加完成！"
echo ""
echo "📊 监控信息："
echo "  - 服务器: $SERVER_NAME ($SERVER_IP)"
echo "  - 类型: $SERVER_TYPE"
echo "  - 环境: $ENVIRONMENT"
echo "  - Node Exporter: http://$SERVER_IP:9100/metrics"
echo ""
echo "🌐 访问地址："
echo "  - Grafana面板: http://localhost:3000"
echo "  - Prometheus Targets: http://localhost:9090/targets"
echo ""
echo "💡 下一步操作："
echo "1. 在Grafana中查看新服务器的监控数据"
echo "2. 根据需要调整告警规则"
echo "3. 为新服务器创建专门的仪表板"
echo ""

# 显示当前所有监控的服务器
echo "📋 当前监控的所有服务器："
curl -s "http://localhost:9090/api/v1/targets" | jq -r '.data.activeTargets[] | select(.labels.job | contains("server") or contains("node")) | "  - \(.labels.instance) (\(.health)) - \(.labels.job)"' | sort | uniq

echo ""
echo "✨ 添加服务器操作完成！"
