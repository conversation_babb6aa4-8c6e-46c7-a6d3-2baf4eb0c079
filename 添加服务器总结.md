# 🌐 添加其他服务器到监控系统 - 完整指南

## 📊 当前监控状态

根据刚才的检查，您的监控系统当前状态：

### ✅ 正常运行的服务器 (2台)
- **************:9100** - yuxin (本机服务器)
- **node-exporter:9100** - localhost (容器内监控)

### ❌ 配置中但未连接的服务器 (3台)
- ***************:9100** - 示例服务器1
- ***************:9100** - 示例服务器2  
- ***************:9100** - 示例服务器3

这些是配置文件中的示例服务器，需要替换为您的实际服务器。

## 🚀 添加新服务器的方法

### 方法一：自动化脚本（推荐）

使用我为您创建的自动化脚本：

```bash
./add-remote-server.sh
```

**功能特点**：
- ✅ 自动SSH连接测试
- ✅ 自动部署Node Exporter
- ✅ 自动配置防火墙
- ✅ 自动更新Prometheus配置
- ✅ 自动验证监控状态

### 方法二：快速手动配置

使用简化的手动配置脚本：

```bash
./quick-add-server.sh
```

**适用场景**：
- 无法SSH自动连接的服务器
- 需要手动控制每个步骤
- 特殊网络环境

### 方法三：完全手动配置

按照详细指南手动操作：

```bash
# 查看详细指南
cat 添加远程服务器指南.md
```

## 📋 添加服务器的具体步骤

### 步骤1：在远程服务器上部署Node Exporter

#### 使用Docker（推荐）：
```bash
# 在远程服务器上执行
docker-compose -f remote-node-exporter.yml up -d
```

#### 或直接安装：
```bash
# Ubuntu/Debian
wget https://github.com/prometheus/node_exporter/releases/download/v1.6.1/node_exporter-1.6.1.linux-amd64.tar.gz
tar xvfz node_exporter-1.6.1.linux-amd64.tar.gz
sudo mv node_exporter-1.6.1.linux-amd64/node_exporter /usr/local/bin/
```

### 步骤2：配置防火墙
```bash
# 开放9100端口
sudo ufw allow 9100
# 或只允许监控服务器访问
sudo ufw allow from 您的监控服务器IP to any port 9100
```

### 步骤3：更新Prometheus配置

编辑 `prometheus/prometheus.yml`，在 `remote-servers` 部分添加：

```yaml
  - job_name: 'remote-servers'
    static_configs:
      - targets: 
        - '新服务器IP:9100'  # 添加您的服务器
        labels:
          server_name: '服务器名称'
          server_type: 'web-server'  # 或其他类型
          environment: 'production'
```

### 步骤4：重启Prometheus
```bash
docker compose restart prometheus
```

## 🛠️ 管理工具

### 服务器管理脚本
```bash
./manage-servers.sh
```

**功能包括**：
- 📊 查看所有服务器状态
- 🔍 查看服务器详细信息
- 🔗 测试服务器连通性
- ➕ 添加新服务器指导
- 🗑️ 移除服务器
- 🌐 快速打开监控面板

### 当前可用的脚本：

1. **add-remote-server.sh** - 全自动添加服务器
2. **quick-add-server.sh** - 半自动添加服务器
3. **manage-servers.sh** - 服务器管理中心
4. **manage-local-server.sh** - 本机服务器管理

## 🎯 不同类型服务器的配置示例

### Web服务器
```yaml
- job_name: 'web-servers'
  static_configs:
    - targets: ['************:9100', '************:9100']
      labels:
        server_type: 'web-server'
        environment: 'production'
        role: 'frontend'
```

### 数据库服务器
```yaml
- job_name: 'database-servers'
  static_configs:
    - targets: ['************:9100']
      labels:
        server_type: 'database-server'
        environment: 'production'
        role: 'mysql'
```

### 测试服务器
```yaml
- job_name: 'test-servers'
  static_configs:
    - targets: ['*************:9100']
      labels:
        server_type: 'test-server'
        environment: 'testing'
```

## 🔍 验证和故障排除

### 检查监控状态
```bash
# 查看所有目标状态
curl -s "http://localhost:9090/api/v1/targets" | jq '.data.activeTargets[] | {instance: .labels.instance, health: .health}'

# 检查特定服务器
curl -s "http://服务器IP:9100/metrics"
```

### 常见问题解决

#### 1. 连接超时
- 检查防火墙：`sudo ufw status`
- 测试端口：`telnet 服务器IP 9100`
- 检查Node Exporter：`docker ps | grep node-exporter`

#### 2. 权限问题
- 确保Node Exporter有系统访问权限
- 检查Docker容器权限设置

#### 3. 数据不准确
- 同步服务器时间：`ntpdate -s time.nist.gov`
- 检查时区设置

## 🎉 添加成功后的效果

添加服务器成功后，您将在Grafana中看到：

- 📈 **新服务器的实时监控图表**
- 🖥️ **CPU、内存、磁盘使用率**
- 🌐 **网络流量统计**
- 📊 **系统负载和进程信息**
- 🔔 **基于预设规则的告警**

## 💡 最佳实践

1. **标准化命名**：使用一致的服务器命名规则
2. **分组管理**：按功能或环境对服务器分组
3. **监控分级**：对不同重要性的服务器设置不同监控频率
4. **安全配置**：使用防火墙限制9100端口访问
5. **定期维护**：定期检查监控状态和更新配置

## 🌐 访问地址

- **Grafana监控面板**: http://localhost:3000
- **Prometheus Targets**: http://localhost:9090/targets
- **Prometheus查询**: http://localhost:9090
- **AlertManager**: http://localhost:9093

现在您可以开始添加其他服务器到监控系统中了！推荐使用 `./add-remote-server.sh` 脚本进行自动化部署。
