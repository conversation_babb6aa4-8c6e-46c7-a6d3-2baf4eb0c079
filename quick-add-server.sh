#!/bin/bash

# 快速添加服务器到监控系统（手动配置版本）

echo "⚡ 快速添加服务器到监控系统"
echo "============================"

# 获取服务器IP
read -p "🖥️  请输入服务器IP地址: " SERVER_IP
read -p "🏷️  请输入服务器名称 (可选): " SERVER_NAME

# 验证IP地址
if ! [[ $SERVER_IP =~ ^[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}$ ]]; then
    echo "❌ IP地址格式不正确"
    exit 1
fi

SERVER_NAME=${SERVER_NAME:-"server-$(echo $SERVER_IP | tr '.' '-')"}

echo ""
echo "📋 将添加服务器: $SERVER_NAME ($SERVER_IP)"
echo ""

# 步骤1: 显示远程服务器部署命令
echo "1️⃣ 在远程服务器 $SERVER_IP 上执行以下命令："
echo ""
echo "# SSH登录到远程服务器"
echo "ssh user@$SERVER_IP"
echo ""
echo "# 创建Node Exporter配置文件"
echo "cat > node-exporter.yml << 'EOF'"
cat remote-node-exporter.yml
echo "EOF"
echo ""
echo "# 启动Node Exporter"
echo "docker-compose -f node-exporter.yml up -d"
echo ""
echo "# 开放防火墙端口"
echo "sudo ufw allow 9100"
echo ""
echo "# 测试Node Exporter"
echo "curl http://localhost:9100/metrics"
echo ""

read -p "远程服务器配置完成后，按回车继续..." dummy

# 步骤2: 测试连通性
echo ""
echo "2️⃣ 测试连通性..."
if curl -s --connect-timeout 10 http://$SERVER_IP:9100/metrics > /dev/null; then
    echo "✅ 连通性测试成功"
else
    echo "❌ 连通性测试失败"
    echo "请确保："
    echo "  - Node Exporter正在运行"
    echo "  - 防火墙已开放9100端口"
    echo "  - 网络连接正常"
    read -p "解决问题后按回车继续，或Ctrl+C退出..." dummy
fi

# 步骤3: 更新Prometheus配置
echo ""
echo "3️⃣ 更新Prometheus配置..."

# 备份配置
cp prometheus/prometheus.yml prometheus/prometheus.yml.backup.$(date +%Y%m%d_%H%M%S)

# 检查是否已存在
if grep -q "$SERVER_IP:9100" prometheus/prometheus.yml; then
    echo "⚠️  服务器已存在于配置中"
else
    # 添加新服务器到remote-servers部分
    NEW_LINE="        - '$SERVER_IP:9100'  # $SERVER_NAME"
    
    # 在示例服务器前插入
    if grep -q "*************:9100" prometheus/prometheus.yml; then
        sed -i "/*************:9100/i\\$NEW_LINE" prometheus/prometheus.yml
        echo "✅ 已添加服务器到Prometheus配置"
    else
        echo "⚠️  请手动编辑 prometheus/prometheus.yml 文件"
        echo "在 remote-servers 的 targets 部分添加："
        echo "        - '$SERVER_IP:9100'  # $SERVER_NAME"
    fi
fi

# 步骤4: 重启Prometheus
echo ""
echo "4️⃣ 重启Prometheus..."
docker compose restart prometheus
echo "✅ Prometheus已重启"

# 步骤5: 验证状态
echo ""
echo "5️⃣ 等待配置生效..."
sleep 15

echo ""
echo "🔍 检查监控状态..."
TARGET_STATUS=$(curl -s "http://localhost:9090/api/v1/targets" | jq -r ".data.activeTargets[] | select(.labels.instance==\"$SERVER_IP:9100\") | .health" 2>/dev/null)

if [ "$TARGET_STATUS" = "up" ]; then
    echo "✅ 服务器监控正常"
elif [ "$TARGET_STATUS" = "down" ]; then
    echo "❌ 服务器监控异常"
else
    echo "⚠️  正在检测中..."
fi

echo ""
echo "🎉 服务器添加完成！"
echo ""
echo "📊 访问地址："
echo "  - Grafana: http://localhost:3000"
echo "  - Prometheus Targets: http://localhost:9090/targets"
echo ""
echo "💡 提示："
echo "1. 在Grafana中查看新服务器的监控数据"
echo "2. 可以为不同类型的服务器创建不同的仪表板"
echo "3. 根据需要调整告警规则"

# 显示当前所有服务器
echo ""
echo "📋 当前监控的服务器列表："
curl -s "http://localhost:9090/api/v1/targets" | jq -r '.data.activeTargets[] | select(.labels.job | contains("server") or contains("node")) | "  \(.labels.instance) - \(.health)"' 2>/dev/null | sort | uniq
